import Foundation
import CloudKit

/// Handler for CloudKit-based sharing operations
class CloudKitShareHandler {
    
    // MARK: - Properties
    
    private let container: CKContainer
    private let privateDatabase: CKDatabase
    private let sharedDatabase: CKDatabase
    
    // MARK: - Initialization
    
    init(container: CKContainer = CKContainer.default()) {
        self.container = container
        self.privateDatabase = container.privateCloudDatabase
        self.sharedDatabase = container.sharedCloudDatabase
    }
    
    // MARK: - Public Methods
    
    /// Check if CloudKit sharing is available
    /// - Returns: True if CloudKit is available and user is signed in
    func isAvailable() async -> Bool {
        do {
            let accountStatus = try await container.accountStatus()
            return accountStatus == .available
        } catch {
            return false
        }
    }
    
    /// Create an iCloud share for the provided data
    /// - Parameters:
    ///   - data: The shareable data to share
    ///   - permissions: Sharing permissions configuration
    /// - Returns: URL of the iCloud share
    func createICloudShare(
        data: ShareableData,
        permissions: ICloudSharePermissions
    ) async throws -> URL {
        // Check if CloudKit is available
        guard await isAvailable() else {
            throw ShareError.icloudUnavailable
        }
        
        do {
            // Create a record for the shareable data
            let record = try createShareRecord(from: data)
            
            // Save the record to private database first
            let savedRecord = try await privateDatabase.save(record)
            
            // Create a share for the record
            let share = CKShare(rootRecord: savedRecord)
            
            // Configure share permissions
            configureSharePermissions(share, permissions: permissions)
            
            // Save the share
            let savedShare = try await privateDatabase.save(share)

            // For now, we'll create a placeholder URL
            // In a real implementation, you would need to use CloudKit's sharing APIs properly
            // This is a simplified version for compilation purposes
            let shareURL = URL(string: "https://www.icloud.com/share/\(savedShare.recordID.recordName)")!

            return shareURL
            
        } catch let error as CKError {
            throw ShareError.icloudShareFailed("CloudKit error: \(error.localizedDescription)")
        } catch {
            throw ShareError.icloudShareFailed("Unknown error: \(error.localizedDescription)")
        }
    }
    
    /// Accept an iCloud share and retrieve the shared data
    /// - Parameter shareURL: URL of the iCloud share
    /// - Returns: The shared data
    func acceptICloudShare(_ shareURL: URL) async throws -> ShareableData {
        do {
            // Get share metadata first
            let shareMetadata = try await container.shareMetadata(for: shareURL)

            // Accept the share
            let acceptShareOperation = CKAcceptSharesOperation(shareMetadatas: [shareMetadata])

            return try await withCheckedThrowingContinuation { continuation in
                acceptShareOperation.acceptSharesResultBlock = { result in
                    switch result {
                    case .success:
                        // Share accepted, now fetch the data
                        Task {
                            do {
                                let data = try await self.fetchSharedData(from: shareURL)
                                continuation.resume(returning: data)
                            } catch {
                                continuation.resume(throwing: error)
                            }
                        }
                    case .failure(let error):
                        continuation.resume(throwing: ShareError.icloudShareFailed("Failed to accept share: \(error.localizedDescription)"))
                    }
                }

                container.add(acceptShareOperation)
            }
            
        } catch let error as ShareError {
            throw error
        } catch {
            throw ShareError.icloudShareFailed("Failed to accept share: \(error.localizedDescription)")
        }
    }
    
    /// Fetch shared data from an accepted share
    /// - Parameter shareURL: URL of the accepted share
    /// - Returns: The shared data
    func fetchSharedData(from shareURL: URL) async throws -> ShareableData {
        do {
            // Extract the share metadata from URL
            let shareMetadata = try await container.shareMetadata(for: shareURL)

            // Fetch the root record from shared database
            guard let rootRecordID = shareMetadata.hierarchicalRootRecordID else {
                throw ShareError.invalidContent
            }

            let record = try await sharedDatabase.record(for: rootRecordID)
            
            // Parse the record back to ShareableData
            let shareableData = try parseShareRecord(record)
            
            return shareableData
            
        } catch let error as CKError {
            throw ShareError.icloudShareFailed("CloudKit error: \(error.localizedDescription)")
        } catch {
            throw ShareError.icloudShareFailed("Failed to fetch shared data: \(error.localizedDescription)")
        }
    }
    
    /// Get information about a share without accepting it
    /// - Parameter shareURL: URL of the share
    /// - Returns: Preview information about the share
    func getSharePreview(_ shareURL: URL) async -> ImportPreview? {
        do {
            let shareMetadata = try await container.shareMetadata(for: shareURL)
            
            // Extract basic information from metadata
            let contentType: ShareContentType = .llmInstance // Default, should be extracted from metadata
            let itemNames = [shareMetadata.share.recordID.recordName] // Placeholder
            
            return ImportPreview(
                contentType: contentType,
                itemCount: 1,
                itemNames: itemNames,
                formatVersion: "1.0",
                exportedAt: shareMetadata.share.creationDate ?? Date(),
                metadata: ShareableMetadata()
            )
            
        } catch {
            return nil
        }
    }
    
    /// Stop sharing a previously shared item
    /// - Parameter shareURL: URL of the share to stop
    func stopSharing(_ shareURL: URL) async throws {
        do {
            let shareMetadata = try await container.shareMetadata(for: shareURL)
            let share = shareMetadata.share
            
            // Delete the share
            try await privateDatabase.deleteRecord(withID: share.recordID)
            
        } catch let error as CKError {
            throw ShareError.icloudShareFailed("Failed to stop sharing: \(error.localizedDescription)")
        } catch {
            throw ShareError.icloudShareFailed("Failed to stop sharing: \(error.localizedDescription)")
        }
    }
    
    // MARK: - Private Methods
    
    private func createShareRecord(from data: ShareableData) throws -> CKRecord {
        // Create a unique record ID
        let recordID = CKRecord.ID(recordName: "LavaChatShare_\(UUID().uuidString)")
        let record = CKRecord(recordType: "LavaChatShare", recordID: recordID)
        
        // Encode the data to JSON
        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        let jsonData = try encoder.encode(data)
        
        // Store the data in the record
        record["shareData"] = jsonData
        record["shareType"] = data.shareType.rawValue
        record["formatVersion"] = data.formatVersion
        record["exportedAt"] = data.exportedAt
        
        // Add metadata for easier querying
        if let instance = data.data.instance {
            record["instanceName"] = instance.name
        }
        if let session = data.data.chatSession {
            record["sessionTitle"] = session.title
        }
        if let action = data.data.messageAction {
            record["actionName"] = action.name
        }
        if let setting = data.data.chatSessionSetting {
            record["settingName"] = setting.name
        }
        
        return record
    }
    
    private func parseShareRecord(_ record: CKRecord) throws -> ShareableData {
        guard let jsonData = record["shareData"] as? Data else {
            throw ShareError.icloudShareFailed("Invalid share record format")
        }
        
        let decoder = JSONDecoder()
        decoder.dateDecodingStrategy = .iso8601
        
        do {
            return try decoder.decode(ShareableData.self, from: jsonData)
        } catch {
            throw ShareError.icloudShareFailed("Failed to decode share data: \(error.localizedDescription)")
        }
    }
    
    private func configureSharePermissions(_ share: CKShare, permissions: ICloudSharePermissions) {
        // Set public permission
        if permissions.allowsReadOnly {
            share.publicPermission = .readOnly
        } else if permissions.allowsReadWrite {
            share.publicPermission = .readWrite
        } else {
            share.publicPermission = .none
        }
        
        // Set expiration date if provided
        if permissions.expirationDate != nil {
            // Note: CKShare doesn't have a direct expiration property
            // This would need to be handled at the application level
        }
    }
}
